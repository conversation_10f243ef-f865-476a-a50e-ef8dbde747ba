import 'package:flutter/material.dart';

class ColorPicker extends StatelessWidget {
  final Color currentColor;
  final Function(Color) onColorChanged;

  const ColorPicker(
    this.currentColor, {
    super.key,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: [
        _buildColorChoice(Colors.black),
        _buildColorChoice(Colors.red),
        _buildColorChoice(Colors.green),
        _buildColorChoice(Colors.blue),
        _buildColorChoice(Colors.yellow),
        _buildColorChoice(Colors.purple),
        _buildColorChoice(Colors.orange),
        _buildColorChoice(Colors.teal),
        _buildColorChoice(Colors.pink),
        _buildColorChoice(Colors.indigo),
      ],
    );
  }

  Widget _buildColorChoice(Color color) {
    return GestureDetector(
      onTap: () => onColorChanged(color),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: currentColor == color ? Colors.white : Colors.grey,
            width: 2,
          ),
          boxShadow: [
            if (currentColor == color)
              BoxShadow(
                color: Colors.blue.withOpacity(0.8),
                blurRadius: 4,
                spreadRadius: 2,
              ),
          ],
        ),
      ),
    );
  }
}