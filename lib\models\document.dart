import 'package:flutter/material.dart';

enum DocumentType {
  text,
  spreadsheet
}

class Document {
  final String id;
  final String title;
  final String content;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final Map<int, TextStyle> textStyles;
  final DocumentType type;
  final List<List<String>>? spreadsheetData;

  Document({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.modifiedAt,
    required this.textStyles,
    this.type = DocumentType.text,
    this.spreadsheetData,
  });

  Document copyWith({
    String? title,
    String? content,
    DateTime? modifiedAt,
    Map<int, TextStyle>? textStyles,
    DocumentType? type,
    List<List<String>>? spreadsheetData,
  }) {
    return Document(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      textStyles: textStyles ?? this.textStyles,
      type: type ?? this.type,
      spreadsheetData: spreadsheetData ?? this.spreadsheetData,
    );
  }
}
