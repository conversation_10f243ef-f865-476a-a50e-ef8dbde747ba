import 'package:flutter/material.dart';

enum DocumentType {
  text,
  spreadsheet,
  slideshow
}

class Slide {
  final String id;
  final String title;
  final String content;
  final Map<int, TextStyle> textStyles;

  Slide({
    required this.id,
    required this.title,
    required this.content,
    required this.textStyles,
  });

  Slide copyWith({
    String? title,
    String? content,
    Map<int, TextStyle>? textStyles,
  }) {
    return Slide(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      textStyles: textStyles ?? this.textStyles,
    );
  }
}

class Document {
  final String id;
  final String title;
  final String content;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final Map<int, TextStyle> textStyles;
  final DocumentType type;
  final List<List<String>>? spreadsheetData;
  final List<Slide>? slideshowData;

  Document({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.modifiedAt,
    required this.textStyles,
    required this.type,
    this.spreadsheetData,
    this.slideshowData,
  });

  Document copyWith({
    String? title,
    String? content,
    DateTime? modifiedAt,
    Map<int, TextStyle>? textStyles,
    DocumentType? type,
    List<List<String>>? spreadsheetData,
    List<Slide>? slideshowData,
  }) {
    return Document(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      textStyles: textStyles ?? this.textStyles,
      type: type ?? this.type,
      spreadsheetData: spreadsheetData ?? this.spreadsheetData,
      slideshowData: slideshowData ?? this.slideshowData,
    );
  }
}
