import 'package:flutter/material.dart';

enum DocumentType {
  text,
  spreadsheet,
  slideshow,
  sketchpad
}

class Slide {
  final String id;
  final String title;
  final String content;
  final Map<int, TextStyle> textStyles;

  Slide({
    required this.id,
    required this.title,
    required this.content,
    required this.textStyles,
  });

  Slide copyWith({
    String? title,
    String? content,
    Map<int, TextStyle>? textStyles,
  }) {
    return Slide(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      textStyles: textStyles ?? this.textStyles,
    );
  }
}

class DrawingStroke {
  final List<Offset> points;
  final Color color;
  final double strokeWidth;
  final String tool; // 'pen', 'brush', 'eraser', 'highlighter'

  DrawingStroke({
    required this.points,
    required this.color,
    required this.strokeWidth,
    required this.tool,
  });

  DrawingStroke copyWith({
    List<Offset>? points,
    Color? color,
    double? strokeWidth,
    String? tool,
  }) {
    return DrawingStroke(
      points: points ?? this.points,
      color: color ?? this.color,
      strokeWidth: strokeWidth ?? this.strokeWidth,
      tool: tool ?? this.tool,
    );
  }
}

class SketchpadData {
  final List<DrawingStroke> strokes;
  final Color backgroundColor;
  final double canvasWidth;
  final double canvasHeight;

  SketchpadData({
    required this.strokes,
    this.backgroundColor = const Color(0xFFFFFFFF),
    this.canvasWidth = 800.0,
    this.canvasHeight = 600.0,
  });

  SketchpadData copyWith({
    List<DrawingStroke>? strokes,
    Color? backgroundColor,
    double? canvasWidth,
    double? canvasHeight,
  }) {
    return SketchpadData(
      strokes: strokes ?? this.strokes,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      canvasWidth: canvasWidth ?? this.canvasWidth,
      canvasHeight: canvasHeight ?? this.canvasHeight,
    );
  }
}

class Document {
  final String id;
  final String title;
  final String content;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final Map<int, TextStyle> textStyles;
  final DocumentType type;
  final List<List<String>>? spreadsheetData;
  final List<Slide>? slideshowData;
  final SketchpadData? sketchpadData;

  Document({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.modifiedAt,
    required this.textStyles,
    required this.type,
    this.spreadsheetData,
    this.slideshowData,
    this.sketchpadData,
  });

  Document copyWith({
    String? title,
    String? content,
    DateTime? modifiedAt,
    Map<int, TextStyle>? textStyles,
    DocumentType? type,
    List<List<String>>? spreadsheetData,
    List<Slide>? slideshowData,
    SketchpadData? sketchpadData,
  }) {
    return Document(
      id: id,
      title: title ?? this.title,
      content: content ?? this.content,
      createdAt: createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      textStyles: textStyles ?? this.textStyles,
      type: type ?? this.type,
      spreadsheetData: spreadsheetData ?? this.spreadsheetData,
      slideshowData: slideshowData ?? this.slideshowData,
      sketchpadData: sketchpadData ?? this.sketchpadData,
    );
  }
}
