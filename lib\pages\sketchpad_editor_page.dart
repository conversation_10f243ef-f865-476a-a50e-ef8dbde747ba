import 'package:flutter/material.dart';
import '../models/document.dart';

class SketchpadEditorPage extends StatefulWidget {
  final Document document;
  final Function(Document) onSave;

  const SketchpadEditorPage({
    super.key,
    required this.document,
    required this.onSave,
  });

  @override
  State<SketchpadEditorPage> createState() => _SketchpadEditorPageState();
}

class _SketchpadEditorPageState extends State<SketchpadEditorPage> {
  late TextEditingController _titleController;
  late SketchpadData _sketchpadData;
  
  // Drawing state
  List<DrawingStroke> _currentStrokes = [];
  DrawingStroke? _currentStroke;
  
  // Tool settings
  String _selectedTool = 'pen';
  Color _selectedColor = Colors.black;
  double _strokeWidth = 3.0;
  bool _isDrawing = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.document.title);
    _sketchpadData = widget.document.sketchpadData ?? _createEmptySketchpad();
    _currentStrokes = List.from(_sketchpadData.strokes);
    _titleController.addListener(_autoSave);
  }

  SketchpadData _createEmptySketchpad() {
    return SketchpadData(
      strokes: [],
      backgroundColor: Colors.white,
      canvasWidth: 800.0,
      canvasHeight: 600.0,
    );
  }

  void _autoSave() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _saveDocument();
      }
    });
  }

  void _saveDocument() {
    final updatedSketchpadData = _sketchpadData.copyWith(
      strokes: _currentStrokes,
    );
    
    final updatedDoc = widget.document.copyWith(
      title: _titleController.text,
      modifiedAt: DateTime.now(),
      sketchpadData: updatedSketchpadData,
    );
    
    widget.onSave(updatedDoc);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sketchpad saved')),
    );
  }

  void _clearCanvas() {
    setState(() {
      _currentStrokes.clear();
    });
    _autoSave();
  }

  void _undoLastStroke() {
    if (_currentStrokes.isNotEmpty) {
      setState(() {
        _currentStrokes.removeLast();
      });
      _autoSave();
    }
  }

  void _onPanStart(DragStartDetails details) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    
    setState(() {
      _isDrawing = true;
      _currentStroke = DrawingStroke(
        points: [localPosition],
        color: _selectedTool == 'eraser' ? _sketchpadData.backgroundColor : _selectedColor,
        strokeWidth: _selectedTool == 'eraser' ? _strokeWidth * 2 : _strokeWidth,
        tool: _selectedTool,
      );
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_isDrawing && _currentStroke != null) {
      final RenderBox renderBox = context.findRenderObject() as RenderBox;
      final localPosition = renderBox.globalToLocal(details.globalPosition);
      
      setState(() {
        _currentStroke = _currentStroke!.copyWith(
          points: [..._currentStroke!.points, localPosition],
        );
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_currentStroke != null) {
      setState(() {
        _currentStrokes.add(_currentStroke!);
        _currentStroke = null;
        _isDrawing = false;
      });
      _autoSave();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: TextField(
          controller: _titleController,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: 'Sketchpad Title',
            hintStyle: TextStyle(color: Colors.white70),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveDocument,
            tooltip: 'Save Sketchpad',
          ),
        ],
      ),
      body: Column(
        children: [
          // Drawing Toolbar
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: const Border(bottom: BorderSide(color: Colors.grey)),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Tool Selection
                  _buildToolButton('pen', Icons.edit, 'Pen'),
                  _buildToolButton('brush', Icons.brush, 'Brush'),
                  _buildToolButton('highlighter', Icons.highlight, 'Highlighter'),
                  _buildToolButton('eraser', Icons.cleaning_services, 'Eraser'),
                  
                  const VerticalDivider(),
                  
                  // Stroke Width
                  const Text('Size: '),
                  SizedBox(
                    width: 100,
                    child: Slider(
                      value: _strokeWidth,
                      min: 1.0,
                      max: 20.0,
                      divisions: 19,
                      onChanged: (value) {
                        setState(() {
                          _strokeWidth = value;
                        });
                      },
                    ),
                  ),
                  Text('${_strokeWidth.round()}'),
                  
                  const VerticalDivider(),
                  
                  // Color Picker
                  const Text('Color: '),
                  ..._buildColorPalette(),
                  
                  const VerticalDivider(),
                  
                  // Actions
                  IconButton(
                    icon: const Icon(Icons.undo),
                    onPressed: _undoLastStroke,
                    tooltip: 'Undo',
                  ),
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearCanvas,
                    tooltip: 'Clear Canvas',
                  ),
                ],
              ),
            ),
          ),
          
          // Drawing Canvas
          Expanded(
            child: Container(
              color: _sketchpadData.backgroundColor,
              child: GestureDetector(
                onPanStart: _onPanStart,
                onPanUpdate: _onPanUpdate,
                onPanEnd: _onPanEnd,
                child: CustomPaint(
                  painter: SketchpadPainter(
                    strokes: _currentStrokes,
                    currentStroke: _currentStroke,
                  ),
                  size: Size.infinite,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolButton(String tool, IconData icon, String tooltip) {
    final isSelected = _selectedTool == tool;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: IconButton(
        icon: Icon(icon),
        onPressed: () {
          setState(() {
            _selectedTool = tool;
          });
        },
        tooltip: tooltip,
        style: IconButton.styleFrom(
          backgroundColor: isSelected ? Theme.of(context).primaryColor : null,
          foregroundColor: isSelected ? Colors.white : null,
        ),
      ),
    );
  }

  List<Widget> _buildColorPalette() {
    final colors = [
      Colors.black,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.yellow,
      Colors.orange,
      Colors.purple,
      Colors.pink,
      Colors.brown,
      Colors.grey,
    ];

    return colors.map((color) {
      final isSelected = _selectedColor == color;
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2.0),
        child: GestureDetector(
          onTap: () {
            setState(() {
              _selectedColor = color;
            });
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.black : Colors.grey,
                width: isSelected ? 3 : 1,
              ),
            ),
          ),
        ),
      );
    }).toList();
  }

  @override
  void dispose() {
    _titleController.removeListener(_autoSave);
    _titleController.dispose();
    super.dispose();
  }
}

class SketchpadPainter extends CustomPainter {
  final List<DrawingStroke> strokes;
  final DrawingStroke? currentStroke;

  SketchpadPainter({
    required this.strokes,
    this.currentStroke,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw all completed strokes
    for (final stroke in strokes) {
      _drawStroke(canvas, stroke);
    }
    
    // Draw current stroke being drawn
    if (currentStroke != null) {
      _drawStroke(canvas, currentStroke!);
    }
  }

  void _drawStroke(Canvas canvas, DrawingStroke stroke) {
    if (stroke.points.isEmpty) return;

    final paint = Paint()
      ..color = stroke.color
      ..strokeWidth = stroke.strokeWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    // Apply tool-specific effects
    switch (stroke.tool) {
      case 'highlighter':
        paint.color = stroke.color.withOpacity(0.5);
        break;
      case 'brush':
        paint.strokeCap = StrokeCap.round;
        break;
      case 'eraser':
        paint.blendMode = BlendMode.clear;
        break;
    }

    if (stroke.points.length == 1) {
      // Single point - draw a dot
      canvas.drawCircle(stroke.points.first, stroke.strokeWidth / 2, paint);
    } else {
      // Multiple points - draw path
      final path = Path();
      path.moveTo(stroke.points.first.dx, stroke.points.first.dy);
      
      for (int i = 1; i < stroke.points.length; i++) {
        path.lineTo(stroke.points[i].dx, stroke.points[i].dy);
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
