import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/document.dart';
import '../services/document_storage_service.dart';
import 'document_editor_page.dart';
import 'spreadsheet_editor_page.dart';
import 'slideshow_editor_page.dart';

class DocumentListPage extends StatefulWidget {
  const DocumentListPage({super.key});

  @override
  State<DocumentListPage> createState() => _DocumentListPageState();
}

class _DocumentListPageState extends State<DocumentListPage> {
  final List<Document> _documents = [];
  final _uuid = const Uuid();
  final _storageService = DocumentStorageService.instance;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  /// Load documents from persistent storage
  Future<void> _loadDocuments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final savedDocuments = await _storageService.loadDocuments();
      final isFirstTime = !(await _storageService.isAppInitialized());

      setState(() {
        _documents.clear();
        _documents.addAll(savedDocuments);
        _isLoading = false;
      });

      // Only add sample documents on first launch, not when user has deleted all documents
      if (_documents.isEmpty && isFirstTime) {
        _createNewDocument('Welcome Document', 'Welcome to Document Suite!');
        _createNewDocument('Sample Document', 'This is a sample document.');
        await _saveDocuments();
        await _storageService.markAppAsInitialized();
      } else if (isFirstTime) {
        // Mark as initialized even if documents exist (in case of data migration)
        await _storageService.markAppAsInitialized();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading documents: $e')),
        );
      }
    }
  }

  /// Save documents to persistent storage
  Future<void> _saveDocuments() async {
    try {
      await _storageService.saveDocuments(_documents);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving documents: $e')),
        );
      }
    }
  }

  void _createNewDocument([String? title, String? content, DocumentType type = DocumentType.text]) {
    final newDoc = Document(
      id: _uuid.v4(),
      title: title ?? 'Untitled Document',
      content: content ?? '',
      createdAt: DateTime.now(),
      modifiedAt: DateTime.now(),
      textStyles: {},
      type: type,
      spreadsheetData: type == DocumentType.spreadsheet ? _createEmptySpreadsheet() : null,
      slideshowData: type == DocumentType.slideshow ? _createEmptySlideshow() : null,
    );

    setState(() {
      _documents.add(newDoc);
    });

    // Auto-save after creating a new document
    _saveDocuments();
  }

  List<List<String>> _createEmptySpreadsheet() {
    // Create a 10x10 empty spreadsheet
    return List.generate(10, (_) => List.filled(10, ''));
  }

  List<Slide> _createEmptySlideshow() {
    // Create a slideshow with one empty slide
    return [
      Slide(
        id: _uuid.v4(),
        title: 'Slide 1',
        content: '',
        textStyles: {},
      ),
    ];
  }

  void _openDocument(Document document) {
    Widget editor;

    switch (document.type) {
      case DocumentType.text:
        editor = DocumentEditorPage(
          document: document,
          onSave: (updatedDoc) {
            setState(() {
              final index = _documents.indexWhere((doc) => doc.id == updatedDoc.id);
              if (index >= 0) {
                _documents[index] = updatedDoc;
              }
            });
            _saveDocuments(); // Auto-save to persistent storage
          },
        );
        break;
      case DocumentType.spreadsheet:
        editor = SpreadsheetEditorPage(
          document: document,
          onSave: (updatedDoc) {
            setState(() {
              final index = _documents.indexWhere((doc) => doc.id == updatedDoc.id);
              if (index >= 0) {
                _documents[index] = updatedDoc;
              }
            });
            _saveDocuments(); // Auto-save to persistent storage
          },
        );
        break;
      case DocumentType.slideshow:
        editor = SlideshowEditorPage(
          document: document,
          onSave: (updatedDoc) {
            setState(() {
              final index = _documents.indexWhere((doc) => doc.id == updatedDoc.id);
              if (index >= 0) {
                _documents[index] = updatedDoc;
              }
            });
            _saveDocuments(); // Auto-save to persistent storage
          },
        );
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => editor),
    );
  }

  void _deleteDocument(String id) {
    setState(() {
      _documents.removeWhere((doc) => doc.id == id);
    });
    _saveDocuments(); // Auto-save after deletion
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Documents'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'reset') {
                _showResetDialog();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset',
                child: Row(
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: 8),
                    Text('Reset App'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _documents.isEmpty
              ? const Center(
                  child: Text('No documents yet. Create one by tapping the + button.'),
                )
              : ListView.builder(
              itemCount: _documents.length,
              itemBuilder: (context, index) {
                final doc = _documents[index];
                return ListTile(
                  title: Text(doc.title),
                  subtitle: Text(
                    'Modified: ${_formatDate(doc.modifiedAt)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  leading: Icon(
                    doc.type == DocumentType.text
                        ? Icons.description
                        : doc.type == DocumentType.spreadsheet
                            ? Icons.grid_on
                            : Icons.slideshow,
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _deleteDocument(doc.id),
                  ),
                  onTap: () => _openDocument(doc),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateDocumentDialog();
        },
        tooltip: 'Create New Document',
        child: const Icon(Icons.add),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset App'),
        content: const Text(
          'This will delete all documents and reset the app to its initial state. '
          'This action cannot be undone.\n\n'
          'Are you sure you want to continue?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _resetApp();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetApp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _storageService.resetApp();
      setState(() {
        _documents.clear();
        _isLoading = false;
      });

      // Add default documents since we're back to first-time state
      _createNewDocument('Welcome Document', 'Welcome to Document Suite!');
      _createNewDocument('Sample Document', 'This is a sample document.');
      await _saveDocuments();
      await _storageService.markAppAsInitialized();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('App has been reset successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error resetting app: $e')),
        );
      }
    }
  }

  void _showCreateDocumentDialog() {
    _showDocumentTypeSelection();
  }

  void _showDocumentTypeSelection() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Document Type'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: DocumentType.values.map((type) {
            IconData icon;
            String title;
            String description;

            switch (type) {
              case DocumentType.text:
                icon = Icons.description;
                title = 'Text Document';
                description = 'Create a rich text document with formatting';
                break;
              case DocumentType.spreadsheet:
                icon = Icons.grid_on;
                title = 'Spreadsheet';
                description = 'Create a spreadsheet with rows and columns';
                break;
              case DocumentType.slideshow:
                icon = Icons.slideshow;
                title = 'Slideshow';
                description = 'Create a presentation with multiple slides';
                break;
            }

            return Card(
              margin: const EdgeInsets.symmetric(vertical: 4.0),
              child: ListTile(
                leading: Icon(icon, size: 32, color: Theme.of(context).primaryColor),
                title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
                subtitle: Text(description),
                onTap: () {
                  Navigator.pop(context);
                  _showDocumentNameDialog(type);
                },
                trailing: const Icon(Icons.arrow_forward_ios),
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDocumentNameDialog(DocumentType type) {
    final titleController = TextEditingController();
    String typeDisplayName;

    switch (type) {
      case DocumentType.text:
        typeDisplayName = 'Text Document';
        break;
      case DocumentType.spreadsheet:
        typeDisplayName = 'Spreadsheet';
        break;
      case DocumentType.slideshow:
        typeDisplayName = 'Slideshow';
        break;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Create $typeDisplayName'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              type == DocumentType.text
                  ? Icons.description
                  : type == DocumentType.spreadsheet
                      ? Icons.grid_on
                      : Icons.slideshow,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: titleController,
              decoration: InputDecoration(
                labelText: '$typeDisplayName Title',
                hintText: 'Enter a name for your $typeDisplayName',
                border: const OutlineInputBorder(),
              ),
              autofocus: true,
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  _createNewDocument(value, '', type);
                } else {
                  _createNewDocument('Untitled $typeDisplayName', '', type);
                }
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showDocumentTypeSelection();
            },
            child: const Text('Back'),
          ),
          TextButton(
            onPressed: () {
              if (titleController.text.isNotEmpty) {
                _createNewDocument(titleController.text, '', type);
              } else {
                _createNewDocument('Untitled $typeDisplayName', '', type);
              }
              Navigator.pop(context);
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}


