import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/document.dart';
import 'document_editor_page.dart';
import 'spreadsheet_editor_page.dart';

class DocumentListPage extends StatefulWidget {
  const DocumentListPage({super.key});

  @override
  State<DocumentListPage> createState() => _DocumentListPageState();
}

class _DocumentListPageState extends State<DocumentListPage> {
  // In a real app, this would be stored in a database or file system
  final List<Document> _documents = [];
  final _uuid = const Uuid();

  @override
  void initState() {
    super.initState();
    // Add some sample documents if the list is empty
    if (_documents.isEmpty) {
      _createNewDocument('Welcome Document', 'Welcome to Document Suite!');
      _createNewDocument('Sample Document', 'This is a sample document.');
    }
  }

  void _createNewDocument([String? title, String? content, DocumentType type = DocumentType.text]) {
    final newDoc = Document(
      id: _uuid.v4(),
      title: title ?? 'Untitled Document',
      content: content ?? '',
      createdAt: DateTime.now(),
      modifiedAt: DateTime.now(),
      textStyles: {},
      type: type,
      spreadsheetData: type == DocumentType.spreadsheet ? _createEmptySpreadsheet() : null,
    );

    setState(() {
      _documents.add(newDoc);
    });
  }

  List<List<String>> _createEmptySpreadsheet() {
    // Create a 10x10 empty spreadsheet
    return List.generate(10, (_) => List.filled(10, ''));
  }

  void _openDocument(Document document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => document.type == DocumentType.text
            ? DocumentEditorPage(
                document: document,
                onSave: (updatedDoc) {
                  setState(() {
                    final index = _documents.indexWhere((doc) => doc.id == updatedDoc.id);
                    if (index >= 0) {
                      _documents[index] = updatedDoc;
                    }
                  });
                },
              )
            : SpreadsheetEditorPage(
                document: document,
                onSave: (updatedDoc) {
                  setState(() {
                    final index = _documents.indexWhere((doc) => doc.id == updatedDoc.id);
                    if (index >= 0) {
                      _documents[index] = updatedDoc;
                    }
                  });
                },
              ),
      ),
    );
  }

  void _deleteDocument(String id) {
    setState(() {
      _documents.removeWhere((doc) => doc.id == id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Documents'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _documents.isEmpty
          ? const Center(
              child: Text('No documents yet. Create one by tapping the + button.'),
            )
          : ListView.builder(
              itemCount: _documents.length,
              itemBuilder: (context, index) {
                final doc = _documents[index];
                return ListTile(
                  title: Text(doc.title),
                  subtitle: Text(
                    'Modified: ${_formatDate(doc.modifiedAt)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  leading: Icon(
                    doc.type == DocumentType.text
                        ? Icons.description
                        : Icons.grid_on,
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _deleteDocument(doc.id),
                  ),
                  onTap: () => _openDocument(doc),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateDocumentDialog();
        },
        tooltip: 'Create New Document',
        child: const Icon(Icons.add),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showCreateDocumentDialog() {
    final titleController = TextEditingController();
    DocumentType selectedType = DocumentType.text;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Create New Document'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Document Title',
                  hintText: 'Enter a title for your document',
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Document Type: '),
                  const SizedBox(width: 8),
                  DropdownButton<DocumentType>(
                    value: selectedType,
                    onChanged: (DocumentType? newValue) {
                      if (newValue != null) {
                        setState(() {
                          selectedType = newValue;
                        });
                      }
                    },
                    items: DocumentType.values.map((DocumentType type) {
                      return DropdownMenuItem<DocumentType>(
                        value: type,
                        child: Text(type == DocumentType.text ? 'Text Document' : 'Spreadsheet'),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (titleController.text.isNotEmpty) {
                  _createNewDocument(titleController.text, '', selectedType);
                } else {
                  _createNewDocument('Untitled', '', selectedType);
                }
                Navigator.pop(context);
              },
              child: const Text('Create'),
            ),
          ],
        ),
      ),
    );
  }
}


