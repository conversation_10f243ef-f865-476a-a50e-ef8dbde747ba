import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/document.dart';

class DocumentStorageService {
  static const String _documentsKey = 'documents';
  static const String _initializedKey = 'app_initialized';
  static DocumentStorageService? _instance;
  
  DocumentStorageService._();
  
  static DocumentStorageService get instance {
    _instance ??= DocumentStorageService._();
    return _instance!;
  }

  /// Save all documents to persistent storage
  Future<void> saveDocuments(List<Document> documents) async {
    try {
      final documentsJson = documents.map((doc) => _documentToJson(doc)).toList();
      final jsonString = jsonEncode(documentsJson);

      if (kIsWeb) {
        // Use localStorage for web
        _setWebStorage(_documentsKey, jsonString);
      } else {
        // Use shared_preferences for desktop/mobile
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_documentsKey, jsonString);
      }

      if (kDebugMode) {
        print('Saved ${documents.length} documents to storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving documents: $e');
      }
    }
  }

  /// Load all documents from persistent storage
  Future<List<Document>> loadDocuments() async {
    try {
      String? jsonString;

      if (kIsWeb) {
        // Use localStorage for web
        jsonString = _getWebStorage(_documentsKey);
      } else {
        // Use shared_preferences for desktop/mobile
        final prefs = await SharedPreferences.getInstance();
        jsonString = prefs.getString(_documentsKey);
      }

      if (jsonString == null || jsonString.isEmpty) {
        if (kDebugMode) {
          print('No saved documents found');
        }
        return [];
      }

      final List<dynamic> documentsJson = jsonDecode(jsonString);
      final documents = documentsJson.map((json) => _documentFromJson(json)).toList();
      if (kDebugMode) {
        print('Loaded ${documents.length} documents from storage');
      }
      return documents;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading documents: $e');
      }
      return [];
    }
  }

  /// Clear all documents from storage
  Future<void> clearDocuments() async {
    try {
      if (kIsWeb) {
        _removeWebStorage(_documentsKey);
      } else {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_documentsKey);
      }
      if (kDebugMode) {
        print('Cleared all documents from storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing documents: $e');
      }
    }
  }

  /// Reset app to first-time state (clears documents and initialization flag)
  Future<void> resetApp() async {
    try {
      if (kIsWeb) {
        _removeWebStorage(_documentsKey);
        _removeWebStorage(_initializedKey);
      } else {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_documentsKey);
        await prefs.remove(_initializedKey);
      }
      if (kDebugMode) {
        print('App reset to first-time state');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error resetting app: $e');
      }
    }
  }

  /// Web storage helpers
  void _setWebStorage(String key, String value) {
    try {
      // Use a simple in-memory storage for now
      // In a real web app, you'd use window.localStorage
      _memoryStorage[key] = value;
    } catch (e) {
      if (kDebugMode) {
        print('Error setting web storage: $e');
      }
    }
  }

  String? _getWebStorage(String key) {
    try {
      return _memoryStorage[key];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting web storage: $e');
      }
      return null;
    }
  }

  void _removeWebStorage(String key) {
    try {
      _memoryStorage.remove(key);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing web storage: $e');
      }
    }
  }

  // Simple in-memory storage for demo purposes
  static final Map<String, String> _memoryStorage = {};

  /// Check if the app has been initialized before
  Future<bool> isAppInitialized() async {
    try {
      if (kIsWeb) {
        return _getWebStorage(_initializedKey) == 'true';
      } else {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getBool(_initializedKey) ?? false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking app initialization: $e');
      }
      return false;
    }
  }

  /// Mark the app as initialized
  Future<void> markAppAsInitialized() async {
    try {
      if (kIsWeb) {
        _setWebStorage(_initializedKey, 'true');
      } else {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_initializedKey, true);
      }
      if (kDebugMode) {
        print('App marked as initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error marking app as initialized: $e');
      }
    }
  }

  /// Convert Document to JSON
  Map<String, dynamic> _documentToJson(Document document) {
    return {
      'id': document.id,
      'title': document.title,
      'content': document.content,
      'createdAt': document.createdAt.toIso8601String(),
      'modifiedAt': document.modifiedAt.toIso8601String(),
      'textStyles': _textStylesToJson(document.textStyles),
      'type': document.type.name,
      'spreadsheetData': document.spreadsheetData,
      'slideshowData': document.slideshowData?.map((slide) => _slideToJson(slide)).toList(),
      'sketchpadData': document.sketchpadData != null ? _sketchpadToJson(document.sketchpadData!) : null,
    };
  }

  /// Convert JSON to Document
  Document _documentFromJson(Map<String, dynamic> json) {
    return Document(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      createdAt: DateTime.parse(json['createdAt']),
      modifiedAt: DateTime.parse(json['modifiedAt']),
      textStyles: _textStylesFromJson(json['textStyles']),
      type: DocumentType.values.firstWhere((e) => e.name == json['type']),
      spreadsheetData: json['spreadsheetData'] != null 
          ? List<List<String>>.from(json['spreadsheetData'].map((row) => List<String>.from(row)))
          : null,
      slideshowData: json['slideshowData'] != null
          ? List<Slide>.from(json['slideshowData'].map((slide) => _slideFromJson(slide)))
          : null,
      sketchpadData: json['sketchpadData'] != null
          ? _sketchpadFromJson(json['sketchpadData'])
          : null,
    );
  }

  /// Convert Slide to JSON
  Map<String, dynamic> _slideToJson(Slide slide) {
    return {
      'id': slide.id,
      'title': slide.title,
      'content': slide.content,
      'textStyles': _textStylesToJson(slide.textStyles),
    };
  }

  /// Convert JSON to Slide
  Slide _slideFromJson(Map<String, dynamic> json) {
    return Slide(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      textStyles: _textStylesFromJson(json['textStyles']),
    );
  }

  /// Convert SketchpadData to JSON
  Map<String, dynamic> _sketchpadToJson(SketchpadData sketchpadData) {
    return {
      'strokes': sketchpadData.strokes.map((stroke) => _strokeToJson(stroke)).toList(),
      'backgroundColor': sketchpadData.backgroundColor.value,
      'canvasWidth': sketchpadData.canvasWidth,
      'canvasHeight': sketchpadData.canvasHeight,
    };
  }

  /// Convert JSON to SketchpadData
  SketchpadData _sketchpadFromJson(Map<String, dynamic> json) {
    return SketchpadData(
      strokes: List<DrawingStroke>.from(json['strokes'].map((stroke) => _strokeFromJson(stroke))),
      backgroundColor: Color(json['backgroundColor']),
      canvasWidth: json['canvasWidth']?.toDouble() ?? 800.0,
      canvasHeight: json['canvasHeight']?.toDouble() ?? 600.0,
    );
  }

  /// Convert DrawingStroke to JSON
  Map<String, dynamic> _strokeToJson(DrawingStroke stroke) {
    return {
      'points': stroke.points.map((point) => {'dx': point.dx, 'dy': point.dy}).toList(),
      'color': stroke.color.value,
      'strokeWidth': stroke.strokeWidth,
      'tool': stroke.tool,
    };
  }

  /// Convert JSON to DrawingStroke
  DrawingStroke _strokeFromJson(Map<String, dynamic> json) {
    return DrawingStroke(
      points: List<Offset>.from(json['points'].map((point) => Offset(point['dx'], point['dy']))),
      color: Color(json['color']),
      strokeWidth: json['strokeWidth']?.toDouble() ?? 3.0,
      tool: json['tool'] ?? 'pen',
    );
  }

  /// Convert TextStyles map to JSON
  Map<String, dynamic> _textStylesToJson(Map<int, TextStyle> textStyles) {
    final Map<String, dynamic> result = {};
    
    textStyles.forEach((index, style) {
      result[index.toString()] = {
        'fontSize': style.fontSize,
        'fontWeight': style.fontWeight?.index,
        'fontStyle': style.fontStyle?.index,
        'color': style.color?.value,
        'backgroundColor': style.backgroundColor?.value,
        'decoration': style.decoration?.toString(),
      };
    });
    
    return result;
  }

  /// Convert JSON to TextStyles map
  Map<int, TextStyle> _textStylesFromJson(Map<String, dynamic>? json) {
    if (json == null) return {};
    
    final Map<int, TextStyle> result = {};
    
    json.forEach((indexStr, styleJson) {
      final index = int.parse(indexStr);
      final style = TextStyle(
        fontSize: styleJson['fontSize']?.toDouble(),
        fontWeight: styleJson['fontWeight'] != null 
            ? FontWeight.values[styleJson['fontWeight']]
            : null,
        fontStyle: styleJson['fontStyle'] != null
            ? FontStyle.values[styleJson['fontStyle']]
            : null,
        color: styleJson['color'] != null 
            ? Color(styleJson['color'])
            : null,
        backgroundColor: styleJson['backgroundColor'] != null
            ? Color(styleJson['backgroundColor'])
            : null,
        decoration: styleJson['decoration'] != null
            ? _parseTextDecoration(styleJson['decoration'])
            : null,
      );
      result[index] = style;
    });
    
    return result;
  }

  /// Parse TextDecoration from string
  TextDecoration? _parseTextDecoration(String decorationStr) {
    if (decorationStr.contains('underline') && decorationStr.contains('lineThrough')) {
      return TextDecoration.combine([TextDecoration.underline, TextDecoration.lineThrough]);
    } else if (decorationStr.contains('underline')) {
      return TextDecoration.underline;
    } else if (decorationStr.contains('lineThrough')) {
      return TextDecoration.lineThrough;
    }
    return null;
  }
}
