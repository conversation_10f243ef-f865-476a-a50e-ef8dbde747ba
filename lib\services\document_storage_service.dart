import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/document.dart';

class DocumentStorageService {
  static const String _documentsKey = 'documents';
  static DocumentStorageService? _instance;
  
  DocumentStorageService._();
  
  static DocumentStorageService get instance {
    _instance ??= DocumentStorageService._();
    return _instance!;
  }

  /// Save all documents to persistent storage
  Future<void> saveDocuments(List<Document> documents) async {
    try {
      final documentsJson = documents.map((doc) => _documentToJson(doc)).toList();
      final jsonString = jsonEncode(documentsJson);

      if (kIsWeb) {
        // Use localStorage for web
        _setWebStorage(_documentsKey, jsonString);
      } else {
        // Use shared_preferences for desktop/mobile
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_documentsKey, jsonString);
      }

      if (kDebugMode) {
        print('Saved ${documents.length} documents to storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving documents: $e');
      }
    }
  }

  /// Load all documents from persistent storage
  Future<List<Document>> loadDocuments() async {
    try {
      String? jsonString;

      if (kIsWeb) {
        // Use localStorage for web
        jsonString = _getWebStorage(_documentsKey);
      } else {
        // Use shared_preferences for desktop/mobile
        final prefs = await SharedPreferences.getInstance();
        jsonString = prefs.getString(_documentsKey);
      }

      if (jsonString == null || jsonString.isEmpty) {
        if (kDebugMode) {
          print('No saved documents found');
        }
        return [];
      }

      final List<dynamic> documentsJson = jsonDecode(jsonString);
      final documents = documentsJson.map((json) => _documentFromJson(json)).toList();
      if (kDebugMode) {
        print('Loaded ${documents.length} documents from storage');
      }
      return documents;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading documents: $e');
      }
      return [];
    }
  }

  /// Clear all documents from storage
  Future<void> clearDocuments() async {
    try {
      if (kIsWeb) {
        _removeWebStorage(_documentsKey);
      } else {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_documentsKey);
      }
      if (kDebugMode) {
        print('Cleared all documents from storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing documents: $e');
      }
    }
  }

  /// Web storage helpers
  void _setWebStorage(String key, String value) {
    try {
      // Use a simple in-memory storage for now
      // In a real web app, you'd use window.localStorage
      _memoryStorage[key] = value;
    } catch (e) {
      if (kDebugMode) {
        print('Error setting web storage: $e');
      }
    }
  }

  String? _getWebStorage(String key) {
    try {
      return _memoryStorage[key];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting web storage: $e');
      }
      return null;
    }
  }

  void _removeWebStorage(String key) {
    try {
      _memoryStorage.remove(key);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing web storage: $e');
      }
    }
  }

  // Simple in-memory storage for demo purposes
  static final Map<String, String> _memoryStorage = {};

  /// Convert Document to JSON
  Map<String, dynamic> _documentToJson(Document document) {
    return {
      'id': document.id,
      'title': document.title,
      'content': document.content,
      'createdAt': document.createdAt.toIso8601String(),
      'modifiedAt': document.modifiedAt.toIso8601String(),
      'textStyles': _textStylesToJson(document.textStyles),
      'type': document.type.name,
      'spreadsheetData': document.spreadsheetData,
      'slideshowData': document.slideshowData?.map((slide) => _slideToJson(slide)).toList(),
    };
  }

  /// Convert JSON to Document
  Document _documentFromJson(Map<String, dynamic> json) {
    return Document(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      createdAt: DateTime.parse(json['createdAt']),
      modifiedAt: DateTime.parse(json['modifiedAt']),
      textStyles: _textStylesFromJson(json['textStyles']),
      type: DocumentType.values.firstWhere((e) => e.name == json['type']),
      spreadsheetData: json['spreadsheetData'] != null 
          ? List<List<String>>.from(json['spreadsheetData'].map((row) => List<String>.from(row)))
          : null,
      slideshowData: json['slideshowData'] != null
          ? List<Slide>.from(json['slideshowData'].map((slide) => _slideFromJson(slide)))
          : null,
    );
  }

  /// Convert Slide to JSON
  Map<String, dynamic> _slideToJson(Slide slide) {
    return {
      'id': slide.id,
      'title': slide.title,
      'content': slide.content,
      'textStyles': _textStylesToJson(slide.textStyles),
    };
  }

  /// Convert JSON to Slide
  Slide _slideFromJson(Map<String, dynamic> json) {
    return Slide(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      textStyles: _textStylesFromJson(json['textStyles']),
    );
  }

  /// Convert TextStyles map to JSON
  Map<String, dynamic> _textStylesToJson(Map<int, TextStyle> textStyles) {
    final Map<String, dynamic> result = {};
    
    textStyles.forEach((index, style) {
      result[index.toString()] = {
        'fontSize': style.fontSize,
        'fontWeight': style.fontWeight?.index,
        'fontStyle': style.fontStyle?.index,
        'color': style.color?.value,
        'backgroundColor': style.backgroundColor?.value,
        'decoration': style.decoration?.toString(),
      };
    });
    
    return result;
  }

  /// Convert JSON to TextStyles map
  Map<int, TextStyle> _textStylesFromJson(Map<String, dynamic>? json) {
    if (json == null) return {};
    
    final Map<int, TextStyle> result = {};
    
    json.forEach((indexStr, styleJson) {
      final index = int.parse(indexStr);
      final style = TextStyle(
        fontSize: styleJson['fontSize']?.toDouble(),
        fontWeight: styleJson['fontWeight'] != null 
            ? FontWeight.values[styleJson['fontWeight']]
            : null,
        fontStyle: styleJson['fontStyle'] != null
            ? FontStyle.values[styleJson['fontStyle']]
            : null,
        color: styleJson['color'] != null 
            ? Color(styleJson['color'])
            : null,
        backgroundColor: styleJson['backgroundColor'] != null
            ? Color(styleJson['backgroundColor'])
            : null,
        decoration: styleJson['decoration'] != null
            ? _parseTextDecoration(styleJson['decoration'])
            : null,
      );
      result[index] = style;
    });
    
    return result;
  }

  /// Parse TextDecoration from string
  TextDecoration? _parseTextDecoration(String decorationStr) {
    if (decorationStr.contains('underline') && decorationStr.contains('lineThrough')) {
      return TextDecoration.combine([TextDecoration.underline, TextDecoration.lineThrough]);
    } else if (decorationStr.contains('underline')) {
      return TextDecoration.underline;
    } else if (decorationStr.contains('lineThrough')) {
      return TextDecoration.lineThrough;
    }
    return null;
  }
}
