import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:uuid/uuid.dart';
import '../models/document.dart';
import '../widgets/formatting_toolbar.dart';
import '../main.dart';

class SlideshowEditorPage extends StatefulWidget {
  final Document document;
  final Function(Document) onSave;

  const SlideshowEditorPage({
    super.key,
    required this.document,
    required this.onSave,
  });

  @override
  State<SlideshowEditorPage> createState() => _SlideshowEditorPageState();
}

class _SlideshowEditorPageState extends State<SlideshowEditorPage> {
  late final TextEditingController _titleController;
  late List<Slide> _slides;
  late int _currentSlideIndex;
  RichTextEditingController? _textController;
  late Map<int, TextStyle> _textStyles;
  TextSelection _currentSelection = const TextSelection.collapsed(offset: 0);
  final _uuid = const Uuid();

  // Current formatting state
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;
  bool _isStrikethrough = false;
  double _fontSize = 16.0;
  Color _textColor = Colors.black;
  Color _highlightColor = Colors.transparent;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.document.title);
    _slides = List.from(widget.document.slideshowData ?? [_createEmptySlide()]);
    _currentSlideIndex = 0;
    _loadCurrentSlide();
  }

  Slide _createEmptySlide() {
    return Slide(
      id: _uuid.v4(),
      title: 'New Slide',
      content: '',
      textStyles: {},
    );
  }

  void _loadCurrentSlide() {
    if (_slides.isNotEmpty) {
      final currentSlide = _slides[_currentSlideIndex];
      _textStyles = Map.from(currentSlide.textStyles);

      // Dispose old controller if it exists
      if (_textController != null) {
        _textController!.removeListener(_updateSelection);
        _textController!.dispose();
      }

      _textController = RichTextEditingController(textStyles: _textStyles)
        ..text = currentSlide.content;
      _textController!.addListener(_updateSelection);
    }
  }

  void _updateSelection() {
    if (_textController != null) {
      setState(() {
        _currentSelection = _textController!.selection;
      });
      _updateFormattingState();
    }
  }

  void _updateFormattingState() {
    if (_currentSelection.isCollapsed) return;
    
    final int start = _currentSelection.start;
    if (_textStyles.containsKey(start)) {
      final TextStyle style = _textStyles[start]!;
      setState(() {
        _isBold = style.fontWeight == FontWeight.bold;
        _isItalic = style.fontStyle == FontStyle.italic;
        _isUnderline = style.decoration?.contains(TextDecoration.underline) ?? false;
        _isStrikethrough = style.decoration?.contains(TextDecoration.lineThrough) ?? false;
        _fontSize = style.fontSize ?? 16.0;
        _textColor = style.color ?? Colors.black;
        _highlightColor = style.backgroundColor ?? Colors.transparent;
      });
    }
  }

  void _applyFormattingToSelection({
    bool? bold,
    bool? italic,
    bool? underline,
    bool? strikethrough,
    double? fontSize,
    Color? textColor,
    Color? highlightColor,
  }) {
    if (_currentSelection.isCollapsed) return;
    
    final int start = _currentSelection.start;
    final int end = _currentSelection.end;
    
    setState(() {
      for (int i = start; i < end; i++) {
        final TextStyle existingStyle = _textStyles[i] ?? const TextStyle();
        
        // Build decoration list
        List<TextDecoration> decorations = [];
        if (underline == true || (underline == null && existingStyle.decoration?.contains(TextDecoration.underline) == true)) {
          decorations.add(TextDecoration.underline);
        }
        if (strikethrough == true || (strikethrough == null && existingStyle.decoration?.contains(TextDecoration.lineThrough) == true)) {
          decorations.add(TextDecoration.lineThrough);
        }
        
        _textStyles[i] = TextStyle(
          fontWeight: bold == true ? FontWeight.bold : (bold == false ? FontWeight.normal : existingStyle.fontWeight),
          fontStyle: italic == true ? FontStyle.italic : (italic == false ? FontStyle.normal : existingStyle.fontStyle),
          decoration: decorations.isNotEmpty ? TextDecoration.combine(decorations) : null,
          fontSize: fontSize ?? existingStyle.fontSize,
          color: textColor ?? existingStyle.color,
          backgroundColor: highlightColor ?? existingStyle.backgroundColor,
        );
      }
      
      // Update state variables
      if (bold != null) _isBold = bold;
      if (italic != null) _isItalic = italic;
      if (underline != null) _isUnderline = underline;
      if (strikethrough != null) _isStrikethrough = strikethrough;
      if (fontSize != null) _fontSize = fontSize;
      if (textColor != null) _textColor = textColor;
      if (highlightColor != null) _highlightColor = highlightColor;
    });
  }

  void _saveCurrentSlide() {
    if (_slides.isNotEmpty && _textController != null) {
      _slides[_currentSlideIndex] = _slides[_currentSlideIndex].copyWith(
        content: _textController!.text,
        textStyles: _textStyles,
      );
    }
  }

  void _addNewSlide() {
    _saveCurrentSlide();
    setState(() {
      _slides.add(_createEmptySlide());
      _currentSlideIndex = _slides.length - 1;
      _loadCurrentSlide();
    });
  }

  void _deleteCurrentSlide() {
    if (_slides.length > 1) {
      setState(() {
        _slides.removeAt(_currentSlideIndex);
        if (_currentSlideIndex >= _slides.length) {
          _currentSlideIndex = _slides.length - 1;
        }
        _loadCurrentSlide();
      });
    }
  }

  void _goToSlide(int index) {
    if (index >= 0 && index < _slides.length) {
      _saveCurrentSlide();
      setState(() {
        _currentSlideIndex = index;
        _loadCurrentSlide();
      });
    }
  }

  void _saveDocument() {
    _saveCurrentSlide();
    final updatedDoc = widget.document.copyWith(
      title: _titleController.text,
      modifiedAt: DateTime.now(),
      slideshowData: _slides,
    );
    
    widget.onSave(updatedDoc);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Slideshow saved')),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _textController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: TextField(
          controller: _titleController,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: 'Slideshow Title',
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveDocument,
          ),
        ],
      ),
      body: Row(
        children: [
          // Slide navigation panel
          Container(
            width: 200,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Theme.of(context).dividerColor),
              ),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _addNewSlide,
                          icon: const Icon(Icons.add),
                          label: const Text('New Slide'),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: _slides.length,
                    itemBuilder: (context, index) {
                      final slide = _slides[index];
                      final isSelected = index == _currentSlideIndex;
                      
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                        decoration: BoxDecoration(
                          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
                          border: Border.all(
                            color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                          ),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: ListTile(
                          dense: true,
                          title: Text(
                            'Slide ${index + 1}',
                            style: TextStyle(
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                          subtitle: Text(
                            slide.content.isEmpty ? 'Empty slide' : slide.content.substring(0, slide.content.length > 20 ? 20 : slide.content.length),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          trailing: _slides.length > 1 ? IconButton(
                            icon: const Icon(Icons.delete, size: 16),
                            onPressed: () => _deleteCurrentSlide(),
                          ) : null,
                          onTap: () => _goToSlide(index),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Main editing area
          Expanded(
            child: Column(
              children: [
                // Formatting toolbar
                FormattingToolbar(
                  isBold: _isBold,
                  isItalic: _isItalic,
                  isUnderline: _isUnderline,
                  isStrikethrough: _isStrikethrough,
                  fontSize: _fontSize,
                  textColor: _textColor,
                  highlightColor: _highlightColor,
                  textAlign: _textAlign,
                  onBoldPressed: () => _applyFormattingToSelection(bold: !_isBold),
                  onItalicPressed: () => _applyFormattingToSelection(italic: !_isItalic),
                  onUnderlinePressed: () => _applyFormattingToSelection(underline: !_isUnderline),
                  onStrikethroughPressed: () => _applyFormattingToSelection(strikethrough: !_isStrikethrough),
                  onFontSizeChanged: (size) => _applyFormattingToSelection(fontSize: size),
                  onTextColorChanged: (color) => _applyFormattingToSelection(textColor: color),
                  onHighlightColorChanged: (color) => _applyFormattingToSelection(highlightColor: color),
                  onTextAlignChanged: (align) {
                    setState(() {
                      _textAlign = align;
                    });
                  },
                  onClearFormattingPressed: () {
                    if (_currentSelection.isCollapsed) return;
                    final int start = _currentSelection.start;
                    final int end = _currentSelection.end;
                    setState(() {
                      for (int i = start; i < end; i++) {
                        _textStyles.remove(i);
                      }
                    });
                  },
                ),
                // Slide editor
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: _textController != null ? RichTextField(
                        controller: _textController!,
                        textStyles: _textStyles,
                        textAlign: _textAlign,
                      ) : const Center(child: Text('Loading...')),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
