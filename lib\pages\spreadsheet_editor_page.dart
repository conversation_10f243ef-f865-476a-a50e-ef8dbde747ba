import 'package:flutter/material.dart';
import '../models/document.dart';
import '../widgets/formatting_toolbar.dart';

class SpreadsheetEditorPage extends StatefulWidget {
  final Document document;
  final Function(Document) onSave;

  const SpreadsheetEditorPage({
    super.key,
    required this.document,
    required this.onSave,
  });

  @override
  State<SpreadsheetEditorPage> createState() => _SpreadsheetEditorPageState();
}

class _SpreadsheetEditorPageState extends State<SpreadsheetEditorPage> {
  late TextEditingController _titleController;
  late List<List<String>> _spreadsheetData;
  late ScrollController _horizontalController;
  late ScrollController _verticalController;
  
  // Current cell being edited
  int? _editingRow;
  int? _editingCol;
  final TextEditingController _cellController = TextEditingController();

  // Formatting state (for future cell formatting features)
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;
  bool _isStrikethrough = false;
  double _fontSize = 14.0;
  Color _textColor = Colors.black;
  Color _highlightColor = Colors.transparent;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.document.title);
    _spreadsheetData = List.from(widget.document.spreadsheetData ?? _createEmptySpreadsheet());
    _horizontalController = ScrollController();
    _verticalController = ScrollController();
  }

  List<List<String>> _createEmptySpreadsheet() {
    // Create a 10x10 empty spreadsheet
    return List.generate(10, (_) => List.filled(10, ''));
  }

  @override
  void dispose() {
    _titleController.dispose();
    _cellController.dispose();
    _horizontalController.dispose();
    _verticalController.dispose();
    super.dispose();
  }

  void _saveDocument() {
    final updatedDoc = widget.document.copyWith(
      title: _titleController.text,
      modifiedAt: DateTime.now(),
      spreadsheetData: _spreadsheetData,
    );
    
    widget.onSave(updatedDoc);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Spreadsheet saved')),
    );
  }

  void _startEditingCell(int row, int col) {
    setState(() {
      _editingRow = row;
      _editingCol = col;
      _cellController.text = _spreadsheetData[row][col];
    });
  }

  void _stopEditingCell() {
    if (_editingRow != null && _editingCol != null) {
      setState(() {
        _spreadsheetData[_editingRow!][_editingCol!] = _cellController.text;
        _editingRow = null;
        _editingCol = null;
      });
    }
  }

  void _addRow() {
    setState(() {
      _spreadsheetData.add(List.filled(_spreadsheetData[0].length, ''));
    });
  }

  void _addColumn() {
    setState(() {
      for (var row in _spreadsheetData) {
        row.add('');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: TextField(
          controller: _titleController,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: 'Spreadsheet Title',
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveDocument,
          ),
        ],
      ),
      body: Column(
        children: [
          FormattingToolbar(
            isBold: _isBold,
            isItalic: _isItalic,
            isUnderline: _isUnderline,
            isStrikethrough: _isStrikethrough,
            fontSize: _fontSize,
            textColor: _textColor,
            highlightColor: _highlightColor,
            textAlign: _textAlign,
            onBoldPressed: () {
              setState(() {
                _isBold = !_isBold;
                // TODO: Apply formatting to selected cells
              });
            },
            onItalicPressed: () {
              setState(() {
                _isItalic = !_isItalic;
                // TODO: Apply formatting to selected cells
              });
            },
            onUnderlinePressed: () {
              setState(() {
                _isUnderline = !_isUnderline;
                // TODO: Apply formatting to selected cells
              });
            },
            onStrikethroughPressed: () {
              setState(() {
                _isStrikethrough = !_isStrikethrough;
                // TODO: Apply formatting to selected cells
              });
            },
            onFontSizeChanged: (size) {
              setState(() {
                _fontSize = size;
                // TODO: Apply formatting to selected cells
              });
            },
            onTextColorChanged: (color) {
              setState(() {
                _textColor = color;
                // TODO: Apply formatting to selected cells
              });
            },
            onHighlightColorChanged: (color) {
              setState(() {
                _highlightColor = color;
                // TODO: Apply formatting to selected cells
              });
            },
            onTextAlignChanged: (align) {
              setState(() {
                _textAlign = align;
                // TODO: Apply formatting to selected cells
              });
            },
            onClearFormattingPressed: () {
              setState(() {
                _isBold = false;
                _isItalic = false;
                _isUnderline = false;
                _isStrikethrough = false;
                _fontSize = 14.0;
                _textColor = Colors.black;
                _highlightColor = Colors.transparent;
                _textAlign = TextAlign.left;
                // TODO: Clear formatting from selected cells
              });
            },
          ),
          // Spreadsheet-specific toolbar
          _buildSpreadsheetToolbar(),
          Expanded(
            child: _buildSpreadsheet(),
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheetToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Row',
            onPressed: _addRow,
          ),
          IconButton(
            icon: const Icon(Icons.add_box),
            tooltip: 'Add Column',
            onPressed: _addColumn,
          ),
          const Spacer(),
          Text('Cell: ${_editingRow != null && _editingCol != null ? '${String.fromCharCode(65 + _editingCol!)}${_editingRow! + 1}' : 'None'}'),
          const SizedBox(width: 16),
          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'Save Spreadsheet',
            onPressed: _saveDocument,
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadsheet() {
    final columnCount = _spreadsheetData.isNotEmpty ? _spreadsheetData[0].length : 0;
    
    // Create column headers (A, B, C, etc.)
    final headerRow = Row(
      children: [
        _buildHeaderCell('', 40), // Empty corner cell
        ...List.generate(columnCount, (index) {
          final columnLetter = String.fromCharCode(65 + index); // A, B, C, ...
          return _buildHeaderCell(columnLetter, 100);
        }),
      ],
    );

    return Column(
      children: [
        headerRow,
        Expanded(
          child: ListView.builder(
            controller: _verticalController,
            itemCount: _spreadsheetData.length,
            itemBuilder: (context, rowIndex) {
              return Row(
                children: [
                  // Row header (1, 2, 3, etc.)
                  _buildHeaderCell('${rowIndex + 1}', 40),
                  
                  // Row cells
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _horizontalController,
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: List.generate(columnCount, (colIndex) {
                          return _buildCell(rowIndex, colIndex);
                        }),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderCell(String text, double width) {
    return Container(
      width: width,
      height: 40,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        border: Border.all(color: Colors.grey.shade400),
      ),
      child: Text(
        text,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildCell(int row, int col) {
    final isEditing = _editingRow == row && _editingCol == col;
    
    return GestureDetector(
      onTap: () {
        _stopEditingCell();
        _startEditingCell(row, col);
      },
      child: Container(
        width: 100,
        height: 40,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          color: isEditing ? Colors.blue.withOpacity(0.1) : Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: isEditing
            ? TextField(
                controller: _cellController,
                autofocus: true,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                onSubmitted: (_) => _stopEditingCell(),
              )
            : Align(
                alignment: Alignment.centerLeft,
                child: Text(_spreadsheetData[row][col]),
              ),
      ),
    );
  }
}