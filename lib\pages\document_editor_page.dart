import 'package:document_suite/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/document.dart';
import '../widgets/color_picker.dart';

class DocumentEditorPage extends StatefulWidget {
  final Document document;
  final Function(Document) onSave;

  const DocumentEditorPage({
    super.key, 
    required this.document,
    required this.onSave,
  });

  @override
  State<DocumentEditorPage> createState() => _DocumentEditorPageState();
}

class _DocumentEditorPageState extends State<DocumentEditorPage> {
  late final RichTextEditingController _textController;
  late final TextEditingController _titleController;
  late Map<int, TextStyle> _textStyles;
  TextSelection _currentSelection = const TextSelection.collapsed(offset: 0);

  // Current formatting state
  bool _isBold = false;
  
  // Add a method to toggle bold formatting
  void _toggleBold() {
    setState(() {
      _isBold = !_isBold;
      _applyFormatting();
    });
  }
  
  void _applyFormatting() {
    if (_currentSelection.isValid) {
      final int start = _currentSelection.start;
      final int end = _currentSelection.end;
      
      for (int i = start; i < end; i++) {
        final TextStyle currentStyle = _textStyles[i] ?? const TextStyle();
        _textStyles[i] = currentStyle.copyWith(
          fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
          fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
          fontSize: _fontSize,
          color: _textColor,
          backgroundColor: _highlightColor,
          decoration: _isUnderline 
              ? TextDecoration.underline 
              : (_isStrikethrough ? TextDecoration.lineThrough : null),
        );
      }
      
      // Update the controller to reflect changes
      _textController.value = TextEditingValue(
        text: _textController.text,
        selection: _currentSelection,
      );
    }
  }
  bool _isItalic = false;
  
  // Add a method to toggle italic formatting
  void _toggleItalic() {
    setState(() {
      _isItalic = !_isItalic;
      _applyFormatting();
    });
  }
  bool _isUnderline = false;
  
  // Add a method to toggle underline formatting
  void _toggleUnderline() {
    setState(() {
      _isUnderline = !_isUnderline;
      _applyFormatting();
    });
  }
  bool _isStrikethrough = false;
  double _fontSize = 16.0;
  
  // Add a method to change font size
  void _changeFontSize(double size) {
    setState(() {
      _fontSize = size;
      _applyFormatting();
    });
  }
  Color _textColor = Colors.black;
  Color _highlightColor = Colors.transparent;
  TextAlign _textAlign = TextAlign.left;
  
  // Add a method to change text alignment
  void _changeTextAlign(TextAlign align) {
    setState(() {
      _textAlign = align;
      _applyFormatting();
    });
  }

  @override
  void initState() {
    super.initState();
    _textStyles = Map.from(widget.document.textStyles);
    _textController = RichTextEditingController(textStyles: _textStyles)
      ..text = widget.document.content;
    _titleController = TextEditingController(text: widget.document.title);
    _textController.addListener(_updateSelection);
  }

  void _updateSelection() {
    final selection = _textController.selection;
    if (selection != _currentSelection) {
      setState(() {
        _currentSelection = selection;
        _updateFormattingState();
      });
    }
  }

  void _updateFormattingState() {
    if (_currentSelection.isCollapsed) {
      // Get style at cursor position
      final style = _textStyles[_currentSelection.baseOffset] ?? const TextStyle();
      _updateStateFromStyle(style);
    } else {
      // Check if all selected text has the same formatting
      // For simplicity, just check the first character's style
      final style = _textStyles[_currentSelection.baseOffset] ?? const TextStyle();
      _updateStateFromStyle(style);
    }
  }

  void _updateStateFromStyle(TextStyle style) {
    setState(() {
      _isBold = style.fontWeight == FontWeight.bold;
      _isItalic = style.fontStyle == FontStyle.italic;
      _isUnderline = style.decoration == TextDecoration.underline;
      _isStrikethrough = style.decoration == TextDecoration.lineThrough;
      _fontSize = style.fontSize ?? 16.0;
      _textColor = style.color ?? Colors.black;
      _highlightColor = style.backgroundColor ?? Colors.transparent;
    });
  }

  // Rest of the methods remain the same, but add a save method:
  
  void _saveDocument() {
    final updatedDoc = widget.document.copyWith(
      title: _titleController.text,
      content: _textController.text,
      modifiedAt: DateTime.now(),
      textStyles: _textStyles,
    );
    
    widget.onSave(updatedDoc);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Document saved')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: TextField(
          controller: _titleController,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: 'Document Title',
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveDocument,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFormatToolbar(),
          Expanded(
            child: RichTextField(
              controller: _textController,
              textStyles: _textStyles,
              textAlign: _textAlign,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormatToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        border: Border(bottom: BorderSide(color: Colors.grey.shade400)),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // Bold button
            IconButton(
              icon: Icon(Icons.format_bold, 
                color: _isBold ? Colors.blue : Colors.black),
              onPressed: _toggleBold,
              tooltip: 'Bold',
            ),
            // Italic button
            IconButton(
              icon: Icon(Icons.format_italic,
                color: _isItalic ? Colors.blue : Colors.black),
              onPressed: _toggleItalic,
              tooltip: 'Italic',
            ),
            // Underline button
            IconButton(
              icon: Icon(Icons.format_underlined,
                color: _isUnderline ? Colors.blue : Colors.black),
              onPressed: _toggleUnderline,
              tooltip: 'Underline',
            ),
            // Strikethrough button
            IconButton(
              icon: Icon(Icons.strikethrough_s,
                color: _isStrikethrough ? Colors.blue : Colors.black),
              onPressed: () {
                setState(() {
                  _isStrikethrough = !_isStrikethrough;
                  _applyFormatting();
                });
              },
              tooltip: 'Strikethrough',
            ),
            const VerticalDivider(),
            // Text alignment options
            IconButton(
              icon: const Icon(Icons.format_align_left),
              color: _textAlign == TextAlign.left ? Colors.blue : Colors.black,
              onPressed: () => _changeTextAlign(TextAlign.left),
              tooltip: 'Align Left',
            ),
            IconButton(
              icon: const Icon(Icons.format_align_center),
              color: _textAlign == TextAlign.center ? Colors.blue : Colors.black,
              onPressed: () => _changeTextAlign(TextAlign.center),
              tooltip: 'Align Center',
            ),
            IconButton(
              icon: const Icon(Icons.format_align_right),
              color: _textAlign == TextAlign.right ? Colors.blue : Colors.black,
              onPressed: () => _changeTextAlign(TextAlign.right),
              tooltip: 'Align Right',
            ),
            const VerticalDivider(),
            // Font size
            IconButton(
              icon: const Icon(Icons.text_decrease),
              onPressed: () => _changeFontSize(_fontSize - 2),
              tooltip: 'Decrease Font Size',
            ),
            Text('${_fontSize.toInt()}', style: const TextStyle(fontSize: 16)),
            IconButton(
              icon: const Icon(Icons.text_increase),
              onPressed: () => _changeFontSize(_fontSize + 2),
              tooltip: 'Increase Font Size',
            ),
            const VerticalDivider(),
            // Text color
            IconButton(
              icon: Icon(Icons.format_color_text, color: _textColor),
              onPressed: _showColorPicker,
              tooltip: 'Text Color',
            ),
            // Highlight color
            IconButton(
              icon: Icon(Icons.format_color_fill, 
                color: _highlightColor != Colors.transparent ? _highlightColor : Colors.black),
              onPressed: _showHighlightColorPicker,
              tooltip: 'Highlight Color',
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Text Color'),
        content: SingleChildScrollView(
          child: ColorPicker(
            _textColor,
            onColorChanged: (color) {
              setState(() {
                _textColor = color;
                _applyFormatting();
              });
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showHighlightColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Highlight Color'),
        content: SingleChildScrollView(
          child: Column(
            children: [
              ColorPicker(
                _highlightColor == Colors.transparent ? Colors.yellow : _highlightColor,
                onColorChanged: (color) {
                  setState(() {
                    _highlightColor = color;
                    _applyFormatting();
                  });
                },
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _highlightColor = Colors.transparent;
                    _applyFormatting();
                  });
                  Navigator.of(context).pop();
                },
                child: const Text('Clear Highlight'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}













