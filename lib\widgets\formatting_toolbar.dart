import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FormattingToolbar extends StatelessWidget {
  final bool isBold;
  final bool isItalic;
  final bool isUnderline;
  final bool isStrikethrough;
  final double fontSize;
  final Color textColor;
  final Color highlightColor;
  final TextAlign textAlign;
  
  final VoidCallback? onBoldPressed;
  final VoidCallback? onItalicPressed;
  final VoidCallback? onUnderlinePressed;
  final VoidCallback? onStrikethroughPressed;
  final Function(double)? onFontSizeChanged;
  final Function(Color)? onTextColorChanged;
  final Function(Color)? onHighlightColorChanged;
  final Function(TextAlign)? onTextAlignChanged;
  final VoidCallback? onClearFormattingPressed;
  
  const FormattingToolbar({
    super.key,
    this.isBold = false,
    this.isItalic = false,
    this.isUnderline = false,
    this.isStrikethrough = false,
    this.fontSize = 16.0,
    this.textColor = Colors.black,
    this.highlightColor = Colors.transparent,
    this.textAlign = TextAlign.left,
    this.onBoldPressed,
    this.onItalicPressed,
    this.onUnderlinePressed,
    this.onStrikethroughPressed,
    this.onFontSizeChanged,
    this.onTextColorChanged,
    this.onHighlightColorChanged,
    this.onTextAlignChanged,
    this.onClearFormattingPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // Text formatting buttons
            _buildToggleButton(
              context: context,
              icon: Icons.format_bold,
              tooltip: 'Bold (Ctrl+B)',
              isActive: isBold,
              onPressed: onBoldPressed,
            ),
            _buildToggleButton(
              context: context,
              icon: Icons.format_italic,
              tooltip: 'Italic (Ctrl+I)',
              isActive: isItalic,
              onPressed: onItalicPressed,
            ),
            _buildToggleButton(
              context: context,
              icon: Icons.format_underlined,
              tooltip: 'Underline (Ctrl+U)',
              isActive: isUnderline,
              onPressed: onUnderlinePressed,
            ),
            _buildToggleButton(
              context: context,
              icon: Icons.format_strikethrough,
              tooltip: 'Strikethrough',
              isActive: isStrikethrough,
              onPressed: onStrikethroughPressed,
            ),
            
            const VerticalDivider(width: 20),
            
            // Font size controls
            _buildFontSizeDropdown(context),
            
            const VerticalDivider(width: 20),
            
            // Color controls
            _buildColorButton(
              context: context,
              icon: Icons.format_color_text,
              tooltip: 'Text Color',
              color: textColor,
              onColorChanged: onTextColorChanged,
            ),
            _buildColorButton(
              context: context,
              icon: Icons.format_color_fill,
              tooltip: 'Highlight Color',
              color: highlightColor,
              onColorChanged: onHighlightColorChanged,
            ),
            
            const VerticalDivider(width: 20),
            
            // Text alignment
            _buildToggleButton(
              context: context,
              icon: Icons.format_align_left,
              tooltip: 'Align Left',
              isActive: textAlign == TextAlign.left,
              onPressed: () => onTextAlignChanged?.call(TextAlign.left),
            ),
            _buildToggleButton(
              context: context,
              icon: Icons.format_align_center,
              tooltip: 'Align Center',
              isActive: textAlign == TextAlign.center,
              onPressed: () => onTextAlignChanged?.call(TextAlign.center),
            ),
            _buildToggleButton(
              context: context,
              icon: Icons.format_align_right,
              tooltip: 'Align Right',
              isActive: textAlign == TextAlign.right,
              onPressed: () => onTextAlignChanged?.call(TextAlign.right),
            ),
            
            const VerticalDivider(width: 20),
            
            // Font size quick controls
            IconButton(
              icon: const Icon(Icons.text_decrease),
              tooltip: 'Decrease Font Size',
              onPressed: () => onFontSizeChanged?.call(fontSize - 2),
            ),
            IconButton(
              icon: const Icon(Icons.text_increase),
              tooltip: 'Increase Font Size',
              onPressed: () => onFontSizeChanged?.call(fontSize + 2),
            ),
            
            const VerticalDivider(width: 20),
            
            // Clear formatting
            IconButton(
              icon: const Icon(Icons.format_clear),
              tooltip: 'Clear Formatting',
              onPressed: onClearFormattingPressed,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleButton({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required bool isActive,
    required VoidCallback? onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2.0),
      decoration: BoxDecoration(
        color: isActive ? Theme.of(context).primaryColor.withOpacity(0.2) : null,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: IconButton(
        icon: Icon(icon),
        tooltip: tooltip,
        onPressed: onPressed,
        color: isActive ? Theme.of(context).primaryColor : null,
      ),
    );
  }

  Widget _buildFontSizeDropdown(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: DropdownButton<double>(
        value: fontSize,
        underline: const SizedBox(),
        items: [8.0, 10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 24.0, 28.0, 32.0, 36.0, 48.0]
            .map((size) => DropdownMenuItem(
                  value: size,
                  child: Text('${size.toInt()}'),
                ))
            .toList(),
        onChanged: (newSize) {
          if (newSize != null) {
            onFontSizeChanged?.call(newSize);
          }
        },
      ),
    );
  }

  Widget _buildColorButton({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required Color color,
    required Function(Color)? onColorChanged,
  }) {
    return PopupMenuButton<Color>(
      itemBuilder: (context) => [
        Colors.black,
        Colors.red,
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.purple,
        Colors.brown,
        Colors.pink,
        Colors.transparent,
      ].map((color) => PopupMenuItem(
            value: color,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: color == Colors.transparent ? Colors.white : color,
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
              child: color == Colors.transparent
                  ? const Icon(Icons.clear, size: 16)
                  : null,
            ),
          )).toList(),
      onSelected: (selectedColor) => onColorChanged?.call(selectedColor),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2.0),
        child: IconButton(
          icon: Stack(
            children: [
              Icon(icon),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 3,
                  color: color == Colors.transparent ? Colors.grey : color,
                ),
              ),
            ],
          ),
          tooltip: tooltip,
          onPressed: null,
        ),
      ),
    );
  }
}
