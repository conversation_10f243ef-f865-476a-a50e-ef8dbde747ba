import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Document Suite',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        textSelectionTheme: TextSelectionThemeData(
          selectionColor: Colors.blue.withOpacity(0.3),
          cursorColor: Colors.blue,
          selectionHandleColor: Colors.blue,
        ),
      ),
      home: const DocumentEditorPage(title: 'Document Editor'),
    );
  }
}

class DocumentEditorPage extends StatefulWidget {
  const DocumentEditorPage({super.key, required this.title});

  final String title;

  @override
  State<DocumentEditorPage> createState() => _DocumentEditorPageState();
}

class _DocumentEditorPageState extends State<DocumentEditorPage> {
  late final RichTextEditingController _textController;
  final Map<int, TextStyle> _textStyles = {};
  TextSelection _currentSelection = const TextSelection.collapsed(offset: 0);
  
  @override
  void initState() {
    super.initState();
    _textController = RichTextEditingController(textStyles: _textStyles);
    _textController.addListener(_updateSelection);
  }
  
  void _updateSelection() {
    setState(() {
      _currentSelection = _textController.selection;
    });
  }
  
  void _applyBoldToSelection() {
    if (_currentSelection.isCollapsed) return;
    
    final int start = _currentSelection.start;
    final int end = _currentSelection.end;
    
    setState(() {
      for (int i = start; i < end; i++) {
        if (_textStyles.containsKey(i) && _textStyles[i]!.fontWeight == FontWeight.bold) {
          _textStyles.remove(i);
        } else {
          _textStyles[i] = const TextStyle(fontWeight: FontWeight.bold);
        }
      }
    });
  }
  
  @override
  void dispose() {
    _textController.removeListener(_updateSelection);
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              // Save functionality will be added later
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Document saved')),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Formatting toolbar
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                ),
              ),
            ),
            child: Row(
              children: [
                // Bold button
                IconButton(
                  icon: const Icon(Icons.format_bold),
                  tooltip: 'Bold',
                  onPressed: _applyBoldToSelection,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          // Text editor
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: RichTextField(
                controller: _textController,
                textStyles: _textStyles,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RichTextField extends StatelessWidget {
  final RichTextEditingController controller;
  final Map<int, TextStyle> textStyles;
  
  const RichTextField({
    super.key,
    required this.controller,
    required this.textStyles,
  });
  
  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      maxLines: null,
      expands: true,
      decoration: const InputDecoration(
        hintText: 'Start typing your document...',
        border: InputBorder.none,
      ),
    );
  }
}

class RichTextEditingController extends TextEditingController {
  final Map<int, TextStyle> textStyles;
  
  RichTextEditingController({required this.textStyles});
  
  @override
  TextSpan buildTextSpan({required BuildContext context, TextStyle? style, required bool withComposing}) {
    final List<InlineSpan> children = [];
    final String text = value.text;
    
    for (int i = 0; i < text.length; i++) {
      final TextStyle? customStyle = textStyles[i];
      final String char = text[i];
      
      if (customStyle != null) {
        children.add(TextSpan(text: char, style: customStyle));
      } else {
        if (children.isNotEmpty && children.last is TextSpan && (children.last as TextSpan).style == null) {
          final TextSpan lastSpan = children.last as TextSpan;
          children[children.length - 1] = TextSpan(
            text: '${lastSpan.text}$char',
            style: null,
          );
        } else {
          children.add(TextSpan(text: char));
        }
      }
    }
    
    return TextSpan(
      style: style,
      children: children,
    );
  }
}
