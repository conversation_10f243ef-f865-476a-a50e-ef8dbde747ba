import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'pages/document_list_page.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Document Suite',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        textSelectionTheme: TextSelectionThemeData(
          selectionColor: Colors.blue.withOpacity(0.3),
          cursorColor: Colors.blue,
          selectionHandleColor: Colors.blue,
        ),
      ),
      home: const DocumentListPage(),
    );
  }
}

class RichTextEditingController extends TextEditingController {
  final Map<int, TextStyle> textStyles;
  
  RichTextEditingController({required this.textStyles});
  
  @override
  TextSpan buildTextSpan({required BuildContext context, TextStyle? style, required bool withComposing}) {
    final List<InlineSpan> children = [];
    final String text = value.text;
    
    for (int i = 0; i < text.length; i++) {
      final TextStyle? customStyle = textStyles[i];
      final String char = text[i];
      
      if (customStyle != null) {
        children.add(TextSpan(text: char, style: customStyle));
      } else {
        if (children.isNotEmpty && children.last is TextSpan && (children.last as TextSpan).style == null) {
          final TextSpan lastSpan = children.last as TextSpan;
          children[children.length - 1] = TextSpan(
            text: '${lastSpan.text}$char',
            style: null,
          );
        } else {
          children.add(TextSpan(text: char));
        }
      }
    }
    
    return TextSpan(
      style: style,
      children: children,
    );
  }
}

class RichTextField extends StatelessWidget {
  final RichTextEditingController controller;
  final Map<int, TextStyle> textStyles;
  final TextAlign textAlign;
  
  const RichTextField({
    super.key,
    required this.controller,
    required this.textStyles,
    this.textAlign = TextAlign.left,
  });
  
  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      maxLines: null,
      expands: true,
      textAlign: textAlign,
      decoration: const InputDecoration(
        hintText: 'Start typing your document...',
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(16.0),
      ),
    );
  }
}

// Intent classes for keyboard shortcuts
class BoldIntent extends Intent {}
class ItalicIntent extends Intent {}
class UnderlineIntent extends Intent {}
class StrikethroughIntent extends Intent {}
class SaveDocumentIntent extends Intent {}
class AlignLeftIntent extends Intent {}
class AlignCenterIntent extends Intent {}
class AlignRightIntent extends Intent {}
