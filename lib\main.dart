import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Document Suite',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        textSelectionTheme: TextSelectionThemeData(
          selectionColor: Colors.blue.withOpacity(0.3),
          cursorColor: Colors.blue,
          selectionHandleColor: Colors.blue,
        ),
      ),
      home: const DocumentEditorPage(title: 'Document Editor'),
    );
  }
}

class DocumentEditorPage extends StatefulWidget {
  const DocumentEditorPage({super.key, required this.title});

  final String title;

  @override
  State<DocumentEditorPage> createState() => _DocumentEditorPageState();
}

class _DocumentEditorPageState extends State<DocumentEditorPage> {
  late final RichTextEditingController _textController;
  final Map<int, TextStyle> _textStyles = {};
  TextSelection _currentSelection = const TextSelection.collapsed(offset: 0);

  // Current formatting state
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;
  bool _isStrikethrough = false;
  double _fontSize = 16.0;
  Color _textColor = Colors.black;
  Color _highlightColor = Colors.transparent;
  TextAlign _textAlign = TextAlign.left;

  // Explicit getter to ensure type consistency
  RichTextEditingController get textController => _textController;
  
  @override
  void initState() {
    super.initState();
    _textController = RichTextEditingController(textStyles: _textStyles);
    _textController.addListener(_updateSelection);
  }
  
  void _updateSelection() {
    setState(() {
      _currentSelection = _textController.selection;
    });
    _updateFormattingState();
  }
  
  void _updateFormattingState() {
    if (_currentSelection.isCollapsed) return;

    final int start = _currentSelection.start;
    if (_textStyles.containsKey(start)) {
      final TextStyle style = _textStyles[start]!;
      setState(() {
        _isBold = style.fontWeight == FontWeight.bold;
        _isItalic = style.fontStyle == FontStyle.italic;
        _isUnderline = style.decoration?.contains(TextDecoration.underline) ?? false;
        _isStrikethrough = style.decoration?.contains(TextDecoration.lineThrough) ?? false;
        _fontSize = style.fontSize ?? 16.0;
        _textColor = style.color ?? Colors.black;
        _highlightColor = style.backgroundColor ?? Colors.transparent;
      });
    }
  }

  void _applyBoldToSelection() {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(bold: !_isBold);
  }

  void _applyItalicToSelection() {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(italic: !_isItalic);
  }

  void _applyUnderlineToSelection() {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(underline: !_isUnderline);
  }

  void _applyStrikethroughToSelection() {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(strikethrough: !_isStrikethrough);
  }

  void _changeFontSize(double newSize) {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(fontSize: newSize);
  }

  void _changeTextColor(Color color) {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(textColor: color);
  }

  void _changeHighlightColor(Color color) {
    if (_currentSelection.isCollapsed) return;
    _applyFormattingToSelection(highlightColor: color);
  }

  void _changeTextAlignment(TextAlign alignment) {
    setState(() {
      _textAlign = alignment;
    });
    // Note: Text alignment in Flutter TextField is applied to the entire field,
    // not individual selections. For per-paragraph alignment, we'd need a more
    // complex rich text editor implementation.
  }

  void _clearFormattingFromSelection() {
    if (_currentSelection.isCollapsed) return;

    final int start = _currentSelection.start;
    final int end = _currentSelection.end;

    setState(() {
      for (int i = start; i < end; i++) {
        _textStyles.remove(i);
      }
    });
  }

  void _applyFormattingToSelection({
    bool? bold,
    bool? italic,
    bool? underline,
    bool? strikethrough,
    double? fontSize,
    Color? textColor,
    Color? highlightColor,
  }) {
    if (_currentSelection.isCollapsed) return;

    final int start = _currentSelection.start;
    final int end = _currentSelection.end;

    setState(() {
      for (int i = start; i < end; i++) {
        final TextStyle existingStyle = _textStyles[i] ?? const TextStyle();

        // Build decoration list
        List<TextDecoration> decorations = [];
        if (underline == true || (underline == null && existingStyle.decoration?.contains(TextDecoration.underline) == true)) {
          decorations.add(TextDecoration.underline);
        }
        if (strikethrough == true || (strikethrough == null && existingStyle.decoration?.contains(TextDecoration.lineThrough) == true)) {
          decorations.add(TextDecoration.lineThrough);
        }

        _textStyles[i] = TextStyle(
          fontWeight: bold == true ? FontWeight.bold : (bold == false ? FontWeight.normal : existingStyle.fontWeight),
          fontStyle: italic == true ? FontStyle.italic : (italic == false ? FontStyle.normal : existingStyle.fontStyle),
          decoration: decorations.isNotEmpty ? TextDecoration.combine(decorations) : null,
          fontSize: fontSize ?? existingStyle.fontSize,
          color: textColor ?? existingStyle.color,
          backgroundColor: highlightColor ?? existingStyle.backgroundColor,
        );
      }

      // Update state variables
      if (bold != null) _isBold = bold;
      if (italic != null) _isItalic = italic;
      if (underline != null) _isUnderline = underline;
      if (strikethrough != null) _isStrikethrough = strikethrough;
      if (fontSize != null) _fontSize = fontSize;
      if (textColor != null) _textColor = textColor;
      if (highlightColor != null) _highlightColor = highlightColor;
    });
  }
  
  @override
  void dispose() {
    _textController.removeListener(_updateSelection);
    _textController.dispose();
    super.dispose();
  }

  // UI Helper Methods
  Widget _buildToggleButton({
    required IconData icon,
    required String tooltip,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2.0),
      decoration: BoxDecoration(
        color: isActive ? Theme.of(context).primaryColor.withOpacity(0.2) : null,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: IconButton(
        icon: Icon(icon),
        tooltip: tooltip,
        onPressed: onPressed,
        color: isActive ? Theme.of(context).primaryColor : null,
      ),
    );
  }

  Widget _buildFontSizeDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: DropdownButton<double>(
        value: _fontSize,
        underline: const SizedBox(),
        items: [8.0, 10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 24.0, 28.0, 32.0, 36.0, 48.0]
            .map((size) => DropdownMenuItem(
                  value: size,
                  child: Text('${size.toInt()}'),
                ))
            .toList(),
        onChanged: (newSize) {
          if (newSize != null) {
            setState(() {
              _fontSize = newSize;
            });
            _changeFontSize(newSize);
          }
        },
      ),
    );
  }

  Widget _buildColorButton({
    required IconData icon,
    required String tooltip,
    required Color color,
    required Function(Color) onColorChanged,
  }) {
    return PopupMenuButton<Color>(
      itemBuilder: (context) => [
        Colors.black,
        Colors.red,
        Colors.blue,
        Colors.green,
        Colors.orange,
        Colors.purple,
        Colors.brown,
        Colors.pink,
        Colors.transparent,
      ].map((color) => PopupMenuItem(
            value: color,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: color == Colors.transparent ? Colors.white : color,
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
              child: color == Colors.transparent
                  ? const Icon(Icons.clear, size: 16)
                  : null,
            ),
          )).toList(),
      onSelected: onColorChanged,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2.0),
        child: IconButton(
          icon: Stack(
            children: [
              Icon(icon),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 3,
                  color: color == Colors.transparent ? Colors.grey : color,
                ),
              ),
            ],
          ),
          tooltip: tooltip,
          onPressed: null,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              // Save functionality will be added later
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Document saved')),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Formatting toolbar
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                ),
              ),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Text formatting buttons
                  _buildToggleButton(
                    icon: Icons.format_bold,
                    tooltip: 'Bold',
                    isActive: _isBold,
                    onPressed: _applyBoldToSelection,
                  ),
                  _buildToggleButton(
                    icon: Icons.format_italic,
                    tooltip: 'Italic',
                    isActive: _isItalic,
                    onPressed: _applyItalicToSelection,
                  ),
                  _buildToggleButton(
                    icon: Icons.format_underlined,
                    tooltip: 'Underline',
                    isActive: _isUnderline,
                    onPressed: _applyUnderlineToSelection,
                  ),
                  _buildToggleButton(
                    icon: Icons.format_strikethrough,
                    tooltip: 'Strikethrough',
                    isActive: _isStrikethrough,
                    onPressed: _applyStrikethroughToSelection,
                  ),

                  const VerticalDivider(width: 20),

                  // Font size controls
                  _buildFontSizeDropdown(),

                  const VerticalDivider(width: 20),

                  // Color controls
                  _buildColorButton(
                    icon: Icons.format_color_text,
                    tooltip: 'Text Color',
                    color: _textColor,
                    onColorChanged: _changeTextColor,
                  ),
                  _buildColorButton(
                    icon: Icons.format_color_fill,
                    tooltip: 'Highlight Color',
                    color: _highlightColor,
                    onColorChanged: _changeHighlightColor,
                  ),

                  const VerticalDivider(width: 20),

                  // Text alignment
                  _buildToggleButton(
                    icon: Icons.format_align_left,
                    tooltip: 'Align Left',
                    isActive: _textAlign == TextAlign.left,
                    onPressed: () => _changeTextAlignment(TextAlign.left),
                  ),
                  _buildToggleButton(
                    icon: Icons.format_align_center,
                    tooltip: 'Align Center',
                    isActive: _textAlign == TextAlign.center,
                    onPressed: () => _changeTextAlignment(TextAlign.center),
                  ),
                  _buildToggleButton(
                    icon: Icons.format_align_right,
                    tooltip: 'Align Right',
                    isActive: _textAlign == TextAlign.right,
                    onPressed: () => _changeTextAlignment(TextAlign.right),
                  ),

                  const VerticalDivider(width: 20),

                  // Font size quick controls
                  IconButton(
                    icon: const Icon(Icons.text_decrease),
                    tooltip: 'Decrease Font Size',
                    onPressed: () => _changeFontSize(_fontSize - 2),
                  ),
                  IconButton(
                    icon: const Icon(Icons.text_increase),
                    tooltip: 'Increase Font Size',
                    onPressed: () => _changeFontSize(_fontSize + 2),
                  ),

                  const VerticalDivider(width: 20),

                  // Clear formatting
                  IconButton(
                    icon: const Icon(Icons.format_clear),
                    tooltip: 'Clear Formatting',
                    onPressed: _clearFormattingFromSelection,
                  ),
                ],
              ),
            ),
          ),
          // Text editor
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Shortcuts(
                shortcuts: {
                  LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyB): const BoldIntent(),
                  LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyI): const ItalicIntent(),
                  LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyU): const UnderlineIntent(),
                },
                child: Actions(
                  actions: {
                    BoldIntent: CallbackAction<BoldIntent>(
                      onInvoke: (intent) => _applyBoldToSelection(),
                    ),
                    ItalicIntent: CallbackAction<ItalicIntent>(
                      onInvoke: (intent) => _applyItalicToSelection(),
                    ),
                    UnderlineIntent: CallbackAction<UnderlineIntent>(
                      onInvoke: (intent) => _applyUnderlineToSelection(),
                    ),
                  },
                  child: RichTextField(
                    controller: textController,
                    textStyles: _textStyles,
                    textAlign: _textAlign,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RichTextField extends StatelessWidget {
  final RichTextEditingController controller;
  final Map<int, TextStyle> textStyles;
  final TextAlign textAlign;

  const RichTextField({
    super.key,
    required this.controller,
    required this.textStyles,
    this.textAlign = TextAlign.left,
  });
  
  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      maxLines: null,
      expands: true,
      textAlign: textAlign,
      decoration: const InputDecoration(
        hintText: 'Start typing your document...',
        border: InputBorder.none,
      ),
    );
  }
}

class RichTextEditingController extends TextEditingController {
  final Map<int, TextStyle> textStyles;
  
  RichTextEditingController({required this.textStyles});
  
  @override
  TextSpan buildTextSpan({required BuildContext context, TextStyle? style, required bool withComposing}) {
    final List<InlineSpan> children = [];
    final String text = value.text;
    
    for (int i = 0; i < text.length; i++) {
      final TextStyle? customStyle = textStyles[i];
      final String char = text[i];
      
      if (customStyle != null) {
        children.add(TextSpan(text: char, style: customStyle));
      } else {
        if (children.isNotEmpty && children.last is TextSpan && (children.last as TextSpan).style == null) {
          final TextSpan lastSpan = children.last as TextSpan;
          children[children.length - 1] = TextSpan(
            text: '${lastSpan.text}$char',
            style: null,
          );
        } else {
          children.add(TextSpan(text: char));
        }
      }
    }
    
    return TextSpan(
      style: style,
      children: children,
    );
  }
}

// Intent classes for keyboard shortcuts
class BoldIntent extends Intent {
  const BoldIntent();
}

class ItalicIntent extends Intent {
  const ItalicIntent();
}

class UnderlineIntent extends Intent {
  const UnderlineIntent();
}
